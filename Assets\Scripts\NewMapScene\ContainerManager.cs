// dnSpy decompiler from Assembly-CSharp.dll class: NewMapScene.ContainerManager
using Assets.Scripts.Utils;
using DG.Tweening;
using System;
using UnityEngine;
using Utils;

namespace NewMapScene
{
    public class ContainerManager : MonoBehaviour
    {
        //[DebuggerBrowsable(DebuggerBrowsableState.Never)]
        public event ContainerManager.InformPage PageWillBeActive;

        //[DebuggerBrowsable(DebuggerBrowsableState.Never)]
        public event ContainerManager.InformPage PageOpened;

        public int CurrentSlideNo
        {
            get
            {
                return this._currentSlideNo;
            }
        }

        public MapPage PreviousMapPage
        {
            get
            {
                return this.AsMapPage(this._oldSlideNo);
            }
        }

        public void Start()
        {
            SDKDataExtra.IsGaming = false;
            SDKDataExtra.reliveCount = 0;

            float y = 0f;
            float y2 = 1f;
            if (CameraHelper.IsIphoneX)
            {
                y = -0.317f;
                y2 = 1.38f;
                float y3 = -0.34f;
                float y4 = 1.17f;
                Vector3 localPosition = this.SettingButtonSeperator.transform.localPosition;
                localPosition.y = y3;
                this.SettingButtonSeperator.transform.localPosition = localPosition;
                // localPosition = this.InboxButtonSeperator.transform.localPosition;
                // localPosition.y = y3;
                // this.InboxButtonSeperator.transform.localPosition = localPosition;
                localPosition = this.MapButtonSeperator.transform.localPosition;
                localPosition.y = y3;
                this.MapButtonSeperator.transform.localPosition = localPosition;
                localPosition = this.LeaderboardSeperator.transform.localPosition;
                localPosition.y = y3;
                this.LeaderboardSeperator.transform.localPosition = localPosition;
                Vector3 localScale = new Vector3(1f, y4, 1f);
                this.SettingButtonSeperator.transform.localScale = localScale;
                // this.InboxButtonSeperator.transform.localScale = localScale;
                this.MapButtonSeperator.transform.localScale = localScale;
                this.LeaderboardSeperator.transform.localScale = localScale;
            }
            this._bottomPanelScale = this.BottomPanel.localScale.x;
            float num = CameraHelper.Width / this._bottomPanelScale;
            this.Highlight.localScale = new Vector3(100f * num / 6f, y2, 1f);
            this.Highlight.transform.localPosition = new Vector3(0f, y, 0f);
            float width = CameraHelper.Width;
            float num2 = 0.23f;
            this.Setting.localPosition = new Vector3(-1f * width - num2, -3f, 0f);
            // this.Inbox.localPosition = new Vector3(-1f * width - num2, 0f, 0f);
            this.Map.localPosition = new Vector3(0f, 0f, 0f);
            // this.Shop.localPosition = new Vector3(1f * width + num2, 0f, 0f);
            this.Leaderboard.localPosition = new Vector3(1f * width + num2, 0f, 0f);
            // this.Shop.localPosition = new Vector3(2f * width + num2 * 2f, 0f, 0f);
            //for (int i = 0; i < this.ShadowsRight.Length; i++)
            //{
            //    float x = width * 0.5f + 0.175f + (float)i * (width + num2);
            //    this.ShadowsRight[i].transform.localPosition = new Vector3(x, 0f, 0f);
            //}
            //for (int j = 0; j < this.ShadowsLeft.Length; j++)
            //{
            //    float x2 = -width * 0.5f - 0.175f - (float)j * (width + num2);
            //    this.ShadowsLeft[j].transform.localPosition = new Vector3(x2, 0f, 0f);
            //}
            this.MoveToCurrentTarget();
        }

        public MapPage GetActivePage()
        {
            return this.AsMapPage(this._currentSlideNo);
        }

        public void OnSwipe(int direction)
        {
            if (direction < 0)
            {
                this.SlideToRight();
            }
            else
            {
                this.SlideToLeft();
            }
        }

        private void SlideToRight()
        {
            this._currentSlideNo++;
            if (this._currentSlideNo > 1)
            {
                this._currentSlideNo = 1;
            }
            this.MoveToCurrentTarget();
        }

        private void SlideToLeft()
        {
            this._currentSlideNo--;
            if (this._currentSlideNo < -1)
            {
                this._currentSlideNo = -1;
            }
            this.MoveToCurrentTarget();
        }

        private void MoveToCurrentTarget()
        {
            if (this.PageWillBeActive != null)
            {
                this.PageWillBeActive(this.AsMapPage(this._currentSlideNo));
            }
            float width = CameraHelper.Width;
            float num = CameraHelper.Width / this._bottomPanelScale;
            float num2 = 0.23f;
            if (this.slideTween != null && this.slideTween.IsPlaying())
            {
                this.slideTween.Kill(false);
            }
            this.slideTween = DOTween.Sequence();
            this.slideTween.SetEase(Ease.Linear);
            this.Container.DOKill(true);
            this.slideTween.Append(this.Container.DOLocalMoveX((float)(-(float)this._currentSlideNo) * (width + num2), this.PageTransitionDuration, false).SetEase(Ease.OutExpo));
            this.PassiveSetting.SetActive(true);
            // this.PassiveInbox.SetActive(true);
            this.PassiveMap.SetActive(true);
            this.PassiveLeaderboard.SetActive(true);
            // this.PassiveShop.SetActive(true);
            this.ActiveSetting.SetActive(false);
            // this.ActiveInbox.SetActive(false);
            this.ActiveMap.SetActive(false);
            this.ActiveLeaderboard.SetActive(false);
            // this.ActiveShop.SetActive(false);
            int currentSlideNo = this._currentSlideNo;
            float[] array;
            Transform target;
            int num3;
            switch (currentSlideNo + 1)
            {
                case 0: // Setting page selected
                    array = new float[]
                    {
                    -1.5f,  // SettingButton
                    -1f,    // SettingButtonSeperator
                    0f,     // MapButton
                    0.5f,   // MapButtonSeperator
                    1.5f    // LeaderboardButton
                    };
                    this.PassiveSetting.SetActive(false);
                    this.ActiveSetting.SetActive(true);
                    target = this.ActiveSettingIcon;
                    num3 = 0; // Highlight on SettingButton (array[0])
                    goto IL_281;
                // case 1: // Inbox page removed
                //     array = new float[]
                //     {
                //     -2.5f,
                //     -2f,
                //     -1f,
                //     0f,
                //     0.5f,
                //     1f,
                //     1.5f,
                //     2f,
                //     2.5f
                //     };
                //     this.PassiveInbox.SetActive(false);
                //     this.ActiveInbox.SetActive(true);
                //     target = this.ActiveInboxIcon;
                //     num3 = 2;
                //     goto IL_281;
                case 2: // Leaderboard page selected
                    array = new float[]
                    {
                    -1.5f,  // SettingButton
                    -1f,    // SettingButtonSeperator
                    -0.5f,  // MapButton
                    0f,     // MapButtonSeperator
                    0.5f    // LeaderboardButton
                    };
                    this.PassiveLeaderboard.SetActive(false);
                    this.ActiveLeaderboard.SetActive(true);
                    target = this.ActiveLeaderboardIcon;
                    num3 = 4; // Highlight on LeaderboardButton (array[4])
                    goto IL_281;
                // case 4: // Shop page removed
                //     array = new float[]
                //     {
                //     -2.5f,
                //     -2f,
                //     -1.5f,
                //     -1f,
                //     -0.5f,
                //     0f,
                //     0.5f,
                //     1f,
                //     2f
                //     };
                //     this.PassiveShop.SetActive(false);
                //     this.ActiveShop.SetActive(true);
                //     target = this.ActiveShopIcon;
                //     num3 = 8;
                //     goto IL_281;
            }
            // Default case - Map page selected
            array = new float[]
            {
                -1f,    // SettingButton
                -0.5f,  // SettingButtonSeperator
                0f,     // MapButton
                0.5f,   // MapButtonSeperator
                1f      // LeaderboardButton
            };
            this.PassiveMap.SetActive(false);
            this.ActiveMap.SetActive(true);
            target = this.ActiveMapIcon;
            num3 = 2; // Highlight on MapButton
        IL_281:
            float num4 = num / 6f;
            for (int i = 0; i < array.Length; i++)
            {
                array[i] *= num4;
            }
            Vector3 localPosition = this.SettingButton.localPosition;
            localPosition.x = array[0];
            this.slideTween.Join(this.SettingButton.DOLocalMove(localPosition, 0.3f, false));
            localPosition = this.SettingButtonSeperator.localPosition;
            localPosition.x = array[1];
            this.slideTween.Join(this.SettingButtonSeperator.DOLocalMove(localPosition, 0.3f, false));
            // localPosition = this.InboxButton.localPosition;
            // localPosition.x = array[2];
            // this.slideTween.Join(this.InboxButton.DOLocalMove(localPosition, 0.3f, false));
            // localPosition = this.InboxButtonSeperator.localPosition;
            // localPosition.x = array[3];
            // this.slideTween.Join(this.InboxButtonSeperator.DOLocalMove(localPosition, 0.3f, false));
            localPosition = this.MapButton.localPosition;
            localPosition.x = array[2];
            this.slideTween.Join(this.MapButton.DOLocalMove(localPosition, 0.3f, false));
            localPosition = this.MapButtonSeperator.localPosition;
            localPosition.x = array[3];
            this.slideTween.Join(this.MapButtonSeperator.DOLocalMove(localPosition, 0.3f, false));
            localPosition = this.LeaderboardButton.localPosition;
            localPosition.x = array[4];
            this.slideTween.Join(this.LeaderboardButton.DOLocalMove(localPosition, 0.3f, false));
            // localPosition = this.LeaderboardSeperator.localPosition;
            // localPosition.x = array[5];
            // this.slideTween.Join(this.LeaderboardSeperator.DOLocalMove(localPosition, 0.3f, false));
            // localPosition = this.ShopButton.localPosition;
            // localPosition.x = array[8];
            // this.slideTween.Join(this.ShopButton.DOLocalMove(localPosition, 0.3f, false));
            localPosition = this.Highlight.transform.localPosition;
            localPosition.x = array[num3];
            this.Highlight.DOKill(false);
            this.slideTween.Join(this.Highlight.DOLocalMove(localPosition, this.PageTransitionDuration, false).SetEase(Ease.OutExpo));
            this.slideTween.OnComplete(delegate
            {
                if (this.PageOpened != null)
                {
                    this.PageOpened(this.AsMapPage(this._currentSlideNo));
                }
                this.slideTween = null;
            });
            if (this._oldSlideNo == this._currentSlideNo)
            {
                return;
            }
            this._oldSlideNo = this._currentSlideNo;
            target.DOKill(true);
            target.DOPunchScale(Vector3.one * 0.5f, this.MenuTransitionDuration, 3, 1f);
        }

        public void FocusSocial(bool focus)
        {
            this.BottomPanelBlocker.SetActive(focus);
            if (focus && this._sortingSocial == 0)
            {
                this._sortingSocial = 300;
                SortingTools.ChangeContainerSorting(this.LeaderboardButton, "UI", 300, 0);
                this.SocialButtonPinkBorder1.enabled = false;
                this.SocialButtonPinkBorder2.enabled = false;
            }
            else if (!focus && this._sortingSocial == 300)
            {
                this._sortingSocial = 0;
                SortingTools.ChangeContainerSorting(this.LeaderboardButton, "UI", -300, 0);
                this.SocialButtonPinkBorder1.enabled = true;
                this.SocialButtonPinkBorder2.enabled = true;
            }
            this.SettingButtonCollider.enabled = !focus;
            // this.InboxButtonCollider.enabled = !focus;
            this.MapButtonCollider.enabled = !focus;
            this.LeaderboardButtonCollider.enabled = !focus;
            // this.ShopButtonCollider.enabled = !focus;
        }

        public MapPage GetCurrentMapPage()
        {
            return this.AsMapPage(this._currentSlideNo);
        }

        private MapPage AsMapPage(int currentSlideNo)
        {
            switch (currentSlideNo + 1)
            {
                case 0:
                    return MapPage.Setting;

                // case 1: // Inbox/Lives page removed
                //     return MapPage.Lives;

                case 2:
                    //return MapPage.Social;
                    return MapPage.Leaderboard;

                // case 4: // Shop page removed
                //     return MapPage.Shop;
            }
            return MapPage.Map;
        }

        public void SlideToPosition(int position)
        {
            if (position < -1)
            {
                position = -1;
            }
            if (position > 1)
            {
                position = 1;
            }
            this._currentSlideNo = position;
            this.MoveToCurrentTarget();
            // if (position == -1) // Inbox page removed
            //     ADControl.instance.ShowAD(8, null);//8 ��������
            if (position == 1)
                ADControl.instance.ShowAD(9, null);//9 ���а����
        }

        public void SlideToNext(float next)
        {
            this._currentSlideNo += ((next <= 0f) ? 1 : -1);
            this._currentSlideNo = System.Math.Max(-1, System.Math.Min(1, this._currentSlideNo));
            this.MoveToCurrentTarget();
        }

        public void SlideToNearest()
        {
            float width = CameraHelper.Width;
            float x = this.Container.transform.localPosition.x;
            float num = Math.Abs(x);
            int num2 = (int)((num + width * 0.5f) / width);
            int num3 = (x <= 0f) ? 1 : -1;
            this._currentSlideNo = num2 * num3;
            this._currentSlideNo = System.Math.Max(-1, System.Math.Min(1, this._currentSlideNo));
            this.MoveToCurrentTarget();
        }

        public void KillTweens()
        {
            if (this.slideTween != null)
            {
                this.slideTween.Kill(false);
            }
            this.Container.DOKill(false);
        }

        public float PageTransitionDuration;

        public float MenuTransitionDuration;

        public Transform Setting;

        // public Transform Inbox;

        public Transform Map;

        public Transform Leaderboard;

        // public Transform Shop;

        public Transform Container;

        public Transform SettingButton;

        public Transform SettingButtonSeperator;

        // public Transform InboxButton;

        // public Transform InboxButtonSeperator;

        public Transform MapButton;

        public Transform MapButtonSeperator;

        public Transform LeaderboardButton;

        public Transform LeaderboardSeperator;

        // public Transform ShopButton;

        public Transform Highlight;

        public Transform BottomPanel;

        public BoxCollider2D SettingButtonCollider;

        // public BoxCollider2D InboxButtonCollider;

        public BoxCollider2D MapButtonCollider;

        public BoxCollider2D LeaderboardButtonCollider;

        //Leaderboard
        // public BoxCollider2D ShopButtonCollider;

        public GameObject PassiveSetting;

        // public GameObject PassiveInbox;

        public GameObject PassiveMap;

        public GameObject PassiveLeaderboard;

        // public GameObject PassiveShop;

        public GameObject ActiveSetting;

        // public GameObject ActiveInbox;

        public GameObject ActiveMap;

        public GameObject ActiveLeaderboard;

        // public GameObject ActiveShop;

        public Transform ActiveSettingIcon;

        // public Transform ActiveInboxIcon;

        public Transform ActiveMapIcon;

        public Transform ActiveLeaderboardIcon;

        // public Transform ActiveShopIcon;

        //public SpriteRenderer[] ShadowsRight;

        //public SpriteRenderer[] ShadowsLeft;

        public GameObject BottomPanelBlocker;

        public SpriteRenderer SocialButtonPinkBorder1;

        public SpriteRenderer SocialButtonPinkBorder2;

        private int _currentSlideNo;

        private int _previousSlideNo;

        private Sequence slideTween;

        private float _bottomPanelScale;

        private int _oldSlideNo = -1;

        private int _sortingSocial;

        public delegate void InformPage(MapPage mapPage);
    }
}