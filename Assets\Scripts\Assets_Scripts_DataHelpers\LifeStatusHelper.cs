// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.DataHelpers.LifeStatusHelper
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.CasualTools.SimpleProperties.DAO;
using Assets.Scripts.DAO;
using Assets.Scripts.Extensions;
using Assets.Scripts.Logging;
using Assets.Scripts.Utils;
using NewMapScene;
using System;
using UnityEngine;
using Utils;

namespace Assets.Scripts.DataHelpers
{
    public class LifeStatusHelper
    {
        private LifeStatusHelper()
        {
            this.CurrentFillDuration = 1800;
            this.CurrentLiveDonateDuration = 120;
            this._dao = SimpleDbPropertyDao.Instance;
            this._inventory = InventoryHelper.Instance;
            this._onceDataHelper = OnceDataHelper.Instance;
            this.ReInit();
        }

        public int CurrentFillDuration { get; private set; }
        public int CurrentLiveDonateDuration { get; private set; }

        public void ReInit()
        {
            this._lastLifeSpentDate = default(DoubleDate);
            this._lifeCount = -1;
            this._unlimitedLifesDate = new DoubleDate(this._dao.FindValueByKey("life_u"));
            this._unlimitedLifesDuration = this._inventory.GetItemAmount(InventoryItemType.UnlimitedLife);
            LogManager.Debug(LogTags.LifeStatusHelper, "Unlimited life until: {0} with duration: {1}", new object[]
            {
                this._unlimitedLifesDate,
                this._unlimitedLifesDuration
            });
        }

        public static LifeStatusHelper Instance
        {
            get
            {
                if (LifeStatusHelper._instance != null)
                {
                    return LifeStatusHelper._instance;
                }
                LifeStatusHelper._instance = new LifeStatusHelper();
                return LifeStatusHelper._instance;
            }
        }

        private int GetNumberOfLives()
        {
            if (this._lifeCount >= 0)
            {
                return this._lifeCount;
            }
            int itemAmount = this._inventory.GetItemAmount(InventoryItemType.Life);
            this._lifeCount = itemAmount;
            return this._lifeCount;
        }

        public bool UseLife()
        {
            if (this.GetUnlimitedLifeTime() > 0)
            {
                LogManager.Debug(LogTags.LifeStatusHelper, "Using from unlimited lifes. Exp:{0} Dur:{1}", new object[]
                {
                    this._unlimitedLifesDate,
                    this._unlimitedLifesDuration
                });
                return true;
            }
            LifeData lifeData = this.CurrentLifeData();
            if (lifeData.LivesCount <= 0)
            {
                return false;
            }
            // 更改生命值上限 by:oldp 2025年6月11日11:35:18
            if (lifeData.LivesCount == MaximumLifes)
            {
                this._lastLifeSpentDate = CaravanDateTime.Now;
                this.SaveLastLifeSpentDate(true, null);
            }
            lifeData.LivesCount--;
            this._lifeCount = lifeData.LivesCount;
            this.SaveLifeCount(true);
            return true;
        }

        public void IncreaseLife(bool syncNow = true)
        {
            //xiaoming 增加生命
            this._lifeCount = this.GetNumberOfLives();
            this._lifeCount++;
            // 更改生命值上限 by:oldp 2025年6月11日11:35:18
            if (this._lifeCount > MaximumLifes)
            {
                this._lifeCount = MaximumLifes;
            }
            this.SaveLifeCount(syncNow);
        }

        public /*LifeData*/ void UpdateCurrentLiveDonateData()
        {
            int num = this.GetNumberOfLives() + InboxDAO.Instance.GetCount();
            int timeLeft = 0;
            DoubleDate now = CaravanDateTime.Now;
            if (this._lastLifeDonateDate.IsNotSet())
            {
                string text = this._dao.FindValueByKey("lifedonate_dt");
                if (text != null)
                {
                    this._lastLifeDonateDate = new DoubleDate(text);
                }
            }
            if (this._lastLifeDonateDate.IsOfflineDateSet())
            {
                if (num >= MaxAllLife)
                {
                    //return new LifeData(this.CurrentLiveDonateDuration, num);
                    return;
                }
                int num2 = (int)(now - this._lastLifeDonateDate).TotalSeconds;
                int num3 = (int)Mathf.Floor((float)num2 / (float)this.CurrentLiveDonateDuration);
                //UnityEngine.Debug.Log("UpdateCurrentLiveDonateData " + num2 + " " + num3);
                if (num3 > 0)
                {
                    num = System.Math.Min(num3, MaxAllLife - num);
                    TimeSpan timeSpan = TimeSpan.FromSeconds((double)(num2 % this.CurrentLiveDonateDuration));
                    timeLeft = (int)timeSpan.TotalSeconds;
                    this._lastLifeDonateDate = now;
                    this._lastLifeDonateDate.SubstractTime(timeSpan);
                    num = System.Math.Min(num, ADControl.instance.GetWatchVideoRemanentTime(WatchVideoType.LifeDonate));
                    if (num > 0 && LivesPageController.Instance != null)
                    {
                        for (int i = 0; i < num; i++)
                        {
                            LivesPageController.Instance.LivesInboxContainer.AddEntry("Life Donate", DateTime.UtcNow.TotalSeconds().ToString(), (long)007, 1);
                            //InboxDAO.Instance.AddEntry("Life Donate", DateTime.UtcNow.TotalSeconds().ToString(), (long)007, 1);
                        }
                    }
                    this._onceDataHelper.IncreaseKeyBy("lifedonate_in", num3);
                    this.SaveLastLifeDonateSpentDate(true, null);
                    //this.SaveLifeCount(true);
                }
                else if (num2 >= 0)
                {
                    this._waitForServerTime = 0;
                    timeLeft = this.CurrentLiveDonateDuration - num2;
                }
                else
                {
                    if (this._waitForServerTime > 2)
                    {
                        this._waitForServerTime = 0;
                        num2 = this._onceDataHelper.FindValue("lifedonate_bf");
                        this._lastLifeDonateDate = now;
                        this._lastLifeDonateDate.SubstractTime(new TimeSpan(0, 0, num2));
                        this.SaveLastLifeDonateSpentDate(false, null);
                    }
                    this._waitForServerTime++;
                }
            }
            else
            {
                this._lastLifeDonateDate = CaravanDateTime.Now;
                this.SaveLastLifeDonateSpentDate(true, null);
            }
            //return new LifeData(timeLeft, this._lifeCount);
        }

        public LifeData CurrentLifeData()
        {
            int num = this.GetNumberOfLives();
            int timeLeft = 0;
            DoubleDate now = CaravanDateTime.Now;
            LifeData lifeData = new LifeData();
            bool isDataSet = false;
            if (this._lastLifeSpentDate.IsNotSet())
            {
                string text = this._dao.FindValueByKey("life_dt");
                if (text != null)
                {
                    this._lastLifeSpentDate = new DoubleDate(text);
                }
            }
            if (this._lastLifeSpentDate.IsOfflineDateSet())
            {
                // 更改生命值上限 by:oldp 2025年6月11日11:35:18
                if (num >= MaximumLifes)
                {
                    lifeData = new LifeData(this.CurrentFillDuration, num);
                    isDataSet = true;
                    //return ;
                }
                else
                {
                    int num2 = (int)(now - this._lastLifeSpentDate).TotalSeconds;
                    int num3 = (int)Mathf.Floor((float)num2 / (float)this.CurrentFillDuration);
                    if (num3 > 0)
                    {
                        // 更改生命值上限 by:oldp 2025年6月11日11:35:18
                        num = System.Math.Min(MaximumLifes, num + num3);
                        TimeSpan timeSpan = TimeSpan.FromSeconds((double)(num2 % this.CurrentFillDuration));
                        timeLeft = (int)timeSpan.TotalSeconds;
                        this._lastLifeSpentDate = now;
                        this._lastLifeSpentDate.SubstractTime(timeSpan);
                        this._lifeCount = num;
                        this._onceDataHelper.IncreaseKeyBy("life_in", num3);
                        this.SaveLastLifeSpentDate(true, null);
                        this.SaveLifeCount(true);
                    }
                    else if (num2 >= 0)
                    {
                        this._waitForServerTime = 0;
                        timeLeft = this.CurrentFillDuration - num2;
                    }
                    else
                    {
                        if (this._waitForServerTime > 2)
                        {
                            this._waitForServerTime = 0;
                            num2 = this._onceDataHelper.FindValue("life_bf");
                            this._lastLifeSpentDate = now;
                            this._lastLifeSpentDate.SubstractTime(new TimeSpan(0, 0, num2));
                            this.SaveLastLifeSpentDate(false, null);
                        }
                        this._waitForServerTime++;
                    }
                }
            }
            else
            {
                this._lastLifeSpentDate = CaravanDateTime.Now;
                this.SaveLastLifeSpentDate(true, null);
            }
            UpdateCurrentLiveDonateData();
            if (isDataSet == false)
            {
                lifeData = new LifeData(timeLeft, this._lifeCount);
            }
            return lifeData;
        }

        private void SaveLifeCount(bool syncNow = true)
        {
            if (this._lifeCount < 0)
            {
                this._lifeCount = 0;
            }
            this._inventory.SetItemAmount(InventoryItemType.Life, this._lifeCount, true, syncNow);
            LogManager.Debug(LogTags.LifeStatusHelper, "Updating last count  as {0}", new object[]
            {
                this._lifeCount
            });
        }

        private void SaveLastLifeDonateSpentDate(bool check = true, string inventoryLifeTime = null)
        {
            this._dao.UpdateOrCreateValueByKey("lifedonate_dt", this._lastLifeDonateDate.AsString());
            LogManager.Debug(LogTags.LifeStatusHelper, "Updating last life update as {0}", new object[]
            {
                this._lastLifeDonateDate
            });
            if (check)
            {
                CaravanDateTime.RecordDonateTime(inventoryLifeTime);
            }
        }

        private void SaveLastLifeSpentDate(bool check = true, string inventoryLifeTime = null)
        {
            this._dao.UpdateOrCreateValueByKey("life_dt", this._lastLifeSpentDate.AsString());
            LogManager.Debug(LogTags.LifeStatusHelper, "Updating last life update as {0}", new object[]
            {
                this._lastLifeSpentDate
            });
            if (check)
            {
                CaravanDateTime.RecordTime(inventoryLifeTime);
            }
        }

        public void ReSetLifeDonateTime()
        {
            //LogManager.Debug(LogTags.LifeStatusHelper, "Refilling life!", new object[0]);
            CaravanDateTime.ResetDonateTime();
            //this._lifeCount = 5;
            this._lastLifeDonateDate = CaravanDateTime.Now;
            //this.SaveLifeCount(true);
            this.SaveLastLifeDonateSpentDate(true, null);
        }

        public void Refill()
        {
            LogManager.Debug(LogTags.LifeStatusHelper, "Refilling life!", new object[0]);
            CaravanDateTime.ResetTime();
            // 更改生命值上限 by:oldp 2025年6月11日11:35:18
            this._lifeCount = MaximumLifes;
            this._lastLifeSpentDate = CaravanDateTime.Now;
            this.SaveLifeCount(true);
            this.SaveLastLifeSpentDate(true, null);
        }

        public void AddUnlimitedLife(int coinDataLifeTime)
        {
            if (coinDataLifeTime <= 0)
            {
                return;
            }
            int num = coinDataLifeTime * 60 * 60;
            string text = this._dao.FindValueByKey("life_u");
            DoubleDate unlimitedLifesDate = (text != null) ? new DoubleDate(text) : new DoubleDate(DateTime.MinValue, DateTime.MinValue);
            DoubleDate now = CaravanDateTime.Now;
            if (unlimitedLifesDate.SmallerThan(now))
            {
                unlimitedLifesDate = now;
            }
            this.Refill();
            unlimitedLifesDate.AddTime(TimeSpan.FromSeconds((double)num));
            this._unlimitedLifesDate = unlimitedLifesDate;
            this._dao.UpdateOrCreateValueByKey("life_u", unlimitedLifesDate.AsString());
            int num2 = this._inventory.GetItemAmount(InventoryItemType.UnlimitedLife);
            if (num2 == 0)
            {
                num2 = 2;
                FocusListener.MarkTime();
            }
            this._unlimitedLifesDuration = num2 + num;
            this._inventory.SetItemAmount(InventoryItemType.UnlimitedLife, this._unlimitedLifesDuration, true, true);
        }

        public void UpdateUnlimitedLifeFromBackend(int duration, bool nonSynced)
        {
            if (duration <= 0)
            {
                this._unlimitedLifesDate = new DoubleDate(null);
                this._unlimitedLifesDuration = 0;
                this._dao.DeleteByKey("life_u");
                this._inventory.SetItemAmount(InventoryItemType.UnlimitedLife, 0, nonSynced, true);
                return;
            }
            DoubleDate now = CaravanDateTime.Now;
            this.Refill();
            now.AddTime(TimeSpan.FromSeconds((double)duration));
            this._unlimitedLifesDate = now;
            this._dao.UpdateOrCreateValueByKey("life_u", now.AsString());
            FocusListener.MarkTime();
            this._unlimitedLifesDuration = duration;
            this._inventory.SetItemAmount(InventoryItemType.UnlimitedLife, duration, nonSynced, true);
        }

        public int GetUnlimitedLifeTime()
        {
            double totalSeconds = (this._unlimitedLifesDate - CaravanDateTime.Now).TotalSeconds;
            float num = (float)this._unlimitedLifesDuration - FocusListener.RealtimeSinceMark;
            double num2 = System.Math.Min(totalSeconds, (double)num);
            if (num2 >= 0.0)
            {
                return (int)num2;
            }
            return 0;
        }

        public void ResetUnlimitedLifeDonate()
        {
            LogManager.Debug(LogTags.LifeStatusHelper, "Resetting unlimited life.", new object[0]);
            this._unlimitedLifeDonateDate = new DoubleDate(DateTime.MinValue, DateTime.MinValue);
            this._dao.UpdateOrCreateValueByKey("lifedonate_u", this._unlimitedLifeDonateDate.AsString());
        }

        public void ResetUnlimitedLife()
        {
            LogManager.Debug(LogTags.LifeStatusHelper, "Resetting unlimited life.", new object[0]);
            this._unlimitedLifesDate = new DoubleDate(DateTime.MinValue, DateTime.MinValue);
            this._dao.UpdateOrCreateValueByKey("life_u", this._unlimitedLifesDate.AsString());
        }

        public void ResetLifeAfterHack()
        {
            this._lifeCount = 0;
            this._lastLifeSpentDate = CaravanDateTime.Now;
            LogManager.Debug(LogTags.LifeStatusHelper, "Setting life count as 0.", new object[0]);
            this.SaveLifeCount(true);
            this.SaveLastLifeSpentDate(false, null);
            this.ResetUnlimitedLife();
        }

        public void SaveBeforeFocus()
        {
            int num = this.CurrentFillDuration - this.CurrentLifeData().TimeLeft;
            LogManager.Debug(LogTags.LifeStatusHelper, "Saving passed time as {0}", new object[]
            {
                num
            });
            this._onceDataHelper.SetValue("life_bf", num);
        }

        public string GetSyncTime()
        {
            this.CurrentLifeData();
            return this._lastLifeSpentDate.AsString();
        }

        public void UpdateLifeAndTimeFromBackend(int inventoryLife, string inventoryLifeTime)
        {
            this._lifeCount = inventoryLife;
            if (inventoryLifeTime != null)
            {
                try
                {
                    this._lastLifeSpentDate = new DoubleDate(inventoryLifeTime);
                }
                catch (Exception ex)
                {
                    // 更改生命值上限 by:oldp 2025年6月11日11:35:18
                    this._lifeCount = MaximumLifes;
                    LogManager.Error(LogTags.LifeStatusHelper, "Can not parse time from server: {0}", new object[]
                    {
                        ex.Message
                    });
                }
            }
            this._inventory.SetItemAmount(InventoryItemType.Life, this._lifeCount, false, true);
            this.CurrentLifeData();
            this.SaveLastLifeSpentDate(true, inventoryLifeTime);
        }

        public bool CheckEarnedLifeCountIsValid(long serverTime)
        {
            int num = this._onceDataHelper.FindValue("life_in_time");
            if (num <= 0)
            {
                num = 2100;
            }
            long num2 = CaravanDateTime.RecordedServerTime() - (long)num;
            if (num2 < 0L)
            {
                num2 = 0L;
            }
            long num3 = DateTimeExtensions.FromUnixTimeMiliseconds(serverTime).TotalSeconds();
            int num4 = LifeStatusHelper.MaxLifeCountInTime(num2, num3);
            int num5 = this._onceDataHelper.FindValue("life_in");
            LogManager.Debug(LogTags.LifeStatusHelper, "LifeHack control PassedTime:{0}, BeginTime:{1}, EndTime:{2}, EarnedLife:{3}", new object[]
            {
                num,
                num2,
                num3,
                num5
            });
            return num5 <= num4;
        }

        private static int MaxLifeCountInTime(long beginTime, long endTime)
        {
            long num = endTime - beginTime;
            if (num <= 0L)
            {
                return 0;
            }
            return (int)num / 1800;
        }

        public void ResetEarnedLifeCount()
        {
            LifeData lifeData = this.CurrentLifeData();
            this._onceDataHelper.Remove("life_in");
            int num = this.CurrentFillDuration - lifeData.TimeLeft;
            if (num < 0)
            {
                num = 0;
            }
            this._onceDataHelper.SetValue("life_in_time", num + 300);
            LogManager.Debug(LogTags.LifeStatusHelper, "LifeHack earned life count is reset, and passed time saved as {0}.", new object[]
            {
                num
            });
        }

        private int MaxAllLife = 10;

        private const string KeyUnlimited = "life_u";

        private const string KeyLifeTimer = "life_dt";

        private const string KeyBeforeFocus = "life_bf";

        private const string KeyEarnedLife = "life_in";

        private const string KeyPassedTime = "life_in_time";
        /// <summary>
        /// 最大生命值
        /// </summary>
        public const int MaximumLifes = 10;

        public const int DefaultNumberOfSecondsForRefill = 1800;

        private const int LifeHackCheckBufferTime = 300;

        private static LifeStatusHelper _instance;

        private readonly SimpleDbPropertyDao _dao;

        private readonly OnceDataHelper _onceDataHelper;

        private readonly InventoryHelper _inventory;

        private DoubleDate _lastLifeDonateDate;

        private DoubleDate _unlimitedLifeDonateDate;

        private int _lifeCount;

        private DoubleDate _lastLifeSpentDate;

        private DoubleDate _unlimitedLifesDate;

        private int _unlimitedLifesDuration;

        private int _waitForServerTime;
    }
}