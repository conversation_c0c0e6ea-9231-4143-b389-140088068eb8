{"format": 1, "restore": {"e:\\project\\DogBlast_new\\UnityEditor.SpatialTracking.csproj": {}}, "projects": {"e:\\project\\DogBlast_new\\UnityEditor.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEditor.SpatialTracking.csproj", "projectName": "UnityEditor.SpatialTracking", "projectPath": "e:\\project\\DogBlast_new\\UnityEditor.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEditor.SpatialTracking\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj"}, "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.SpatialTracking.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.SpatialTracking.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}, "e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}, "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEditor.TestRunner.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}, "e:\\project\\DogBlast_new\\UnityEngine.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEngine.SpatialTracking.csproj", "projectName": "UnityEngine.SpatialTracking", "projectPath": "e:\\project\\DogBlast_new\\UnityEngine.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEngine.SpatialTracking\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"e:\\project\\DogBlast_new\\UnityEditor.UI.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEditor.UI.csproj"}, "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj": {"projectPath": "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}, "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "e:\\project\\DogBlast_new\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}, "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "e:\\project\\DogBlast_new\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "e:\\project\\DogBlast_new\\Temp\\obj\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.404\\RuntimeIdentifierGraph.json"}}}}}