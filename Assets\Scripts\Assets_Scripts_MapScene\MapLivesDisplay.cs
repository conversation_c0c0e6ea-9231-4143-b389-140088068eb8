// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.MapScene.MapLivesDisplay
using Assets.Scripts.DataHelpers;
using Assets.Scripts.Dialogs;
using Assets.Scripts.Utils;
using I2.Loc;
using MapScene;
using System;
using System.Collections;
using TMPro;
using UnityEngine;
using Utils;

namespace Assets.Scripts.MapScene
{
    public class MapLivesDisplay : MonoBehaviour
    {
        public void Start()
        {
            base.StartCoroutine(this.CheckLife());
        }

        public void OnApplicationPause(bool pauseStatus)
        {
            if (!pauseStatus)
            {
                this.UpdateLives();
            }
        }

        private IEnumerator CheckLife()
        {
            this.UpdateLives();
            yield return Constants.OneSecond;
            base.StartCoroutine(this.CheckLife());
            yield break;
        }

        public void AddLifes()
        {
            // xiaoming AddLifes
            if (this._anActiveDialog)
            {
                return;
            }
            this._anActiveDialog = true;
            int livesCount = LifeStatusHelper.Instance.CurrentLifeData().LivesCount;
            // TODO: 奖励体力弹窗阈值改成 10 原来是 5 by:oldp 2025年6月11日10:31:37
            if (livesCount >= LifeStatusHelper.MaximumLifes)
            {
                Vector3 zero = Vector3.zero;
                zero.y = this.AddLivesButton.transform.position.y - 2f;
                MapManager.Instance.ShowErrorMessageAtPosition(ScriptLocalization.Get("LifesFull"), zero);
                this._anActiveDialog = false;
                return;
            }
            OutOfLivesDialog.RefillStatus = "map";
            OutOfLivesDialog.CreateAndShowOutOfLivesDialog(new Action(this.LivesDialogClosed));
        }

        private void LivesDialogClosed()
        {
            OutOfLivesDialog.RefillStatus = null;
            this.CurrentMapCoinsDisplay.Refresh();
            this._anActiveDialog = false;
        }

        public void UpdateLives()
        {
            int unlimitedLifeTime = LifeStatusHelper.Instance.GetUnlimitedLifeTime();
            if (unlimitedLifeTime > 0)
            {
                this.SwitchToInfinitiveLifes();
                this.LivesTimer.text = StringFormatUtils.FormatSeconds(unlimitedLifeTime, true);
                this.LivesFastLocalize.Term = string.Empty;
            }
            else
            {
                //xiaoming LifeNumber
                LifeData lifeData = LifeStatusHelper.Instance.CurrentLifeData();
                this._timeLeft = lifeData.TimeLeft;
                int livesCount = lifeData.LivesCount;
                // 更改体力上限  by：oldp 2025年6月11日11:30:28
                int num = Mathf.Clamp(livesCount, 0, LifeStatusHelper.MaximumLifes);//System.Math.Min(5, System.Math.Max(0, livesCount));
                if (num == LifeStatusHelper.MaximumLifes)
                {
                    this.SwitchToFull();
                }
                else
                {
                    this.SwitchToTimeLeft();
                    this.LivesTimer.text = StringFormatUtils.FormatSeconds(this._timeLeft, false);
                    this.LivesFastLocalize.Term = string.Empty;
                    this.LivesCount.text = num.ToString();
                }
            }
        }

        private void SwitchToInfinitiveLifes()
        {
            if (this.displayMode == 3)
            {
                return;
            }
            this.displayMode = 3;
            if (this.AddLivesButton.activeSelf)
            {
                this.AddLivesButton.SetActive(false);
            }
            if (!this.NoButtonDisplay.activeSelf)
            {
                this.NoButtonDisplay.SetActive(true);
            }
            //this.LivesTimer.alignment = TextAlignmentOptions.Left;
            //this.LivesTimer.rectTransform.sizeDelta = new Vector2(1.896f, 0.615f);
            //this.LivesTimer.enableAutoSizing = false;
            //this.LivesTimer.fontSize = 5f;
            //Vector3 localPosition = this.LivesTimer.transform.localPosition;
            //localPosition.x = 0.4f;
            //localPosition.y = -0.049f;
            //this.LivesTimer.transform.localPosition = localPosition;
            this.UnlimitedLifeImage.SetActive(true);
            this.LivesCount.gameObject.SetActive(false);
        }

        private void SwitchToTimeLeft()
        {
            if (this.displayMode == 2)
            {
                return;
            }
            this.displayMode = 2;
            if (!this.AddLivesButton.activeSelf)
            {
                this.AddLivesButton.SetActive(true);
            }
            if (this.NoButtonDisplay.activeSelf)
            {
                this.NoButtonDisplay.SetActive(false);
            }
            //this.LivesTimer.enableAutoSizing = false;
            //this.LivesTimer.fontSize = 6f;
            //this.LivesTimer.alignment = TextAlignmentOptions.Left;
            //Vector3 localPosition = this.LivesTimer.transform.localPosition;
            //localPosition.x = -0.104f;
            //localPosition.y = -0.034f;
            //this.LivesTimer.rectTransform.sizeDelta = new Vector2(1.143f, 0.615f);
            //this.LivesTimer.transform.localPosition = localPosition;
            this.UnlimitedLifeImage.SetActive(false);
            this.LivesCount.gameObject.SetActive(true);
        }

        private void SwitchToFull()
        {
            if (this.displayMode == 1)
            {
                return;
            }
            this.displayMode = 1;
            if (!this.AddLivesButton.activeSelf)
            {
                this.AddLivesButton.SetActive(true);
            }
            if (this.NoButtonDisplay.activeSelf)
            {
                this.NoButtonDisplay.SetActive(false);
            }
            //this.LivesTimer.enableAutoSizing = true;
            this.LivesTimer.text = ScriptLocalization.Get("Full");
            this.LivesFastLocalize.Term = "Full";
            //this.LivesTimer.alignment = TextAlignmentOptions.Center;
            //Vector3 localPosition = this.LivesTimer.transform.localPosition;
            //localPosition.x = 0.03f;
            //localPosition.y = -0.034f;
            //this.LivesTimer.rectTransform.sizeDelta = new Vector2(1.64f, 0.615f);
            //this.LivesTimer.transform.localPosition = localPosition;
            this.UnlimitedLifeImage.SetActive(false);
            this.LivesCount.gameObject.SetActive(true);
            this.LivesCount.text = LifeStatusHelper.MaximumLifes.ToString();
        }

        public FastLocalize LivesFastLocalize;

        public TextMeshPro LivesTimer;

        public TextMeshPro LivesCount;

        private int _timeLeft;

        public GameObject UnlimitedLifeImage;

        public GameObject NoButtonDisplay;

        public GameObject AddLivesButton;

        public MapCoinsDisplay CurrentMapCoinsDisplay;

        private bool _anActiveDialog;

        private byte displayMode;
    }
}